#!/usr/bin/env python3
"""
初步调研功能演示脚本

演示 Sprint 2 实现的初步调研功能，包括：
1. 双搜索引擎架构（Tavily + DuckDuckGo）
2. 并行搜索和结果处理
3. 信息源分类和APA引用格式生成
4. 调研范围界定

使用方法：
    python demo_preliminary_research.py
"""

import asyncio
import json
from typing import Dict, Any
import structlog

from src.backend.models.request import SearchKeywords
from src.backend.agents.requirement_analyst.workflows import preliminary_research
from src.backend.agents.search_engines import SearchEngineService
from src.backend.config import get_config

# 配置日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


async def demo_search_engines():
    """演示搜索引擎功能"""
    print("\n" + "="*60)
    print("🔍 搜索引擎功能演示")
    print("="*60)
    
    try:
        # 获取配置
        config = await get_config()
        
        # 创建搜索引擎服务
        search_service = SearchEngineService(config)
        
        # 演示搜索
        query = "人工智能在医疗诊断中的应用"
        print(f"\n📝 搜索查询: {query}")
        
        # 执行搜索（模拟，因为没有真实的API密钥）
        print("\n⚠️  注意: 这是模拟演示，需要配置真实的 Tavily API 密钥才能执行实际搜索")
        print("   请在 config.yaml 中配置 TAVILY_API_KEY")
        
        # 展示搜索引擎架构
        print(f"\n🏗️  搜索引擎架构:")
        print(f"   - 主要引擎: {search_service.selector.primary_engine}")
        print(f"   - 备用引擎: {search_service.selector.fallback_engine}")
        print(f"   - 代理配置: 已启用 (http://127.0.0.1:8118/)")
        print(f"   - 去重机制: 已启用")
        print(f"   - 质量评估: 已启用")
        
    except Exception as e:
        print(f"❌ 搜索引擎演示失败: {e}")


async def demo_preliminary_research():
    """演示初步调研功能"""
    print("\n" + "="*60)
    print("📊 初步调研功能演示")
    print("="*60)
    
    try:
        # 创建示例搜索关键词
        keywords = SearchKeywords(
            requirement_analysis_id="demo-analysis-001",
            primary_keywords=["人工智能", "医疗诊断", "机器学习"],
            secondary_keywords=["深度学习", "神经网络", "图像识别"],
            technical_terms=["artificial intelligence", "medical diagnosis", "deep learning"],
            english_keywords=["AI in healthcare", "medical AI", "diagnostic AI"],
            industry_terms=["智能医疗", "医疗AI", "辅助诊断"]
        )
        
        print(f"\n📝 搜索关键词:")
        print(f"   - 主要关键词: {', '.join(keywords.primary_keywords)}")
        print(f"   - 技术术语: {', '.join(keywords.technical_terms)}")
        print(f"   - 英文关键词: {', '.join(keywords.english_keywords)}")
        
        print(f"\n⚠️  注意: 这是模拟演示，需要配置真实的搜索引擎API密钥")
        print(f"   实际运行时会执行以下步骤:")
        
        print(f"\n🔄 初步调研流程:")
        print(f"   1. 准备搜索查询 (最多5个并行查询)")
        print(f"   2. 并行执行搜索 (Tavily + DuckDuckGo 降级)")
        print(f"   3. 结果去重和质量评估")
        print(f"   4. 信息源分类 (学术、行业、新闻、技术、其他)")
        print(f"   5. 生成APA第七版引用格式")
        print(f"   6. 界定调研范围和推荐下一步")
        
        # 展示预期输出结构
        print(f"\n📋 预期输出结构:")
        expected_output = {
            "information_summary": "初步调研发现：发现 X 个学术资源, Y 个行业报告...",
            "source_list": [
                {
                    "title": "人工智能在医疗诊断中的应用研究",
                    "url": "https://example.com/ai-medical-diagnosis",
                    "source_type": "学术资源",
                    "quality_score": 0.9,
                    "apa_citation": "Zhang, L. (2024). 人工智能在医疗诊断中的应用研究. Retrieved from https://example.com/ai-medical-diagnosis",
                    "content_summary": "综述了AI在医疗诊断领域的最新进展..."
                }
            ],
            "research_scope": {
                "primary_topics": ["人工智能", "医疗诊断", "机器学习"],
                "information_coverage": {"学术资源": 5, "行业报告": 3, "新闻资讯": 2},
                "recommended_next_steps": ["需要补充更多学术资源", "需要获取相关行业报告"]
            },
            "metadata": {
                "search_queries": ["人工智能", "医疗诊断", "机器学习"],
                "total_sources": 10,
                "search_engines_used": ["tavily", "duckduckgo"]
            }
        }
        
        print(json.dumps(expected_output, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"❌ 初步调研演示失败: {e}")


async def demo_quality_assessment():
    """演示质量评估功能"""
    print("\n" + "="*60)
    print("⭐ 质量评估功能演示")
    print("="*60)
    
    from src.backend.agents.search_engines import ResultStandardizer, SearchResult
    
    standardizer = ResultStandardizer()
    
    # 创建示例搜索结果
    high_quality_result = SearchResult(
        title="人工智能在医疗诊断中的应用：系统性综述",
        content="这是一篇关于人工智能在医疗诊断领域应用的系统性综述文章，详细分析了深度学习、机器学习等技术在医学影像诊断、病理诊断、临床决策支持等方面的应用现状、挑战和发展趋势。文章基于近五年来的相关研究，提供了全面的技术分析和实践案例。",
        url="https://arxiv.org/abs/2024.12345",
        source="tavily",
        authors=["张三", "李四", "王五"],
        publication_date="2024-01-15"
    )
    
    low_quality_result = SearchResult(
        title="AI",
        content="短内容",
        url="invalid-url",
        source="duckduckgo"
    )
    
    # 计算质量评分
    high_score = standardizer._calculate_quality_score(high_quality_result)
    low_score = standardizer._calculate_quality_score(low_quality_result)
    
    print(f"\n📊 质量评估示例:")
    print(f"\n🏆 高质量结果 (评分: {high_score:.2f}):")
    print(f"   - 标题: {high_quality_result.title[:50]}...")
    print(f"   - 内容长度: {len(high_quality_result.content)} 字符")
    print(f"   - 作者: {', '.join(high_quality_result.authors)}")
    print(f"   - 发布日期: {high_quality_result.publication_date}")
    
    print(f"\n📉 低质量结果 (评分: {low_score:.2f}):")
    print(f"   - 标题: {low_quality_result.title}")
    print(f"   - 内容长度: {len(low_quality_result.content)} 字符")
    print(f"   - 作者: 无")
    print(f"   - URL: {low_quality_result.url}")
    
    # 生成APA引用
    high_citation = standardizer._generate_apa_citation(high_quality_result)
    low_citation = standardizer._generate_apa_citation(low_quality_result)
    
    print(f"\n📚 APA第七版引用格式:")
    print(f"\n高质量结果引用:")
    print(f"   {high_citation}")
    print(f"\n低质量结果引用:")
    print(f"   {low_citation}")


async def main():
    """主演示函数"""
    print("🚀 Sprint 2 初步调研功能演示")
    print("基于工作流设计文档中的需求分析阶段第3步实现")
    
    # 演示各个功能模块
    await demo_search_engines()
    await demo_preliminary_research()
    await demo_quality_assessment()
    
    print("\n" + "="*60)
    print("✅ 演示完成")
    print("="*60)
    print("\n📝 实现的功能:")
    print("   ✅ 双搜索引擎架构 (Tavily + DuckDuckGo)")
    print("   ✅ 智能降级机制")
    print("   ✅ 并行搜索处理")
    print("   ✅ URL去重和结果标准化")
    print("   ✅ 质量评估算法")
    print("   ✅ APA第七版引用格式生成")
    print("   ✅ 信息源分类")
    print("   ✅ 调研范围界定")
    print("   ✅ 网络代理支持")
    print("   ✅ 错误处理和重试机制")
    print("   ✅ 完整的测试用例")
    
    print("\n🔧 配置要求:")
    print("   - 配置 Tavily API 密钥 (可选，有免费额度)")
    print("   - 确保代理服务器运行在 http://127.0.0.1:8118/")
    print("   - 安装所有必需依赖 (已完成)")
    
    print("\n🧪 测试验证:")
    print("   - 运行 `uv run python -m pytest test/test_search_engines.py -v`")
    print("   - 运行 `uv run python -m pytest test/test_preliminary_research.py -v`")
    
    print("\n📖 相关文档:")
    print("   - doc/design/20250730.multi.agent.workflow.design.v1.0.md")
    print("   - doc/dev/implementation/20250804.search.engine.integration.guide.v1.1.md")
    print("   - doc/dev/sprints/planning/20250804.sprint2.planning.v1.1.md")


if __name__ == "__main__":
    asyncio.run(main())
