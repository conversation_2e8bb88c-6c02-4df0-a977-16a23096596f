# 路径配置指南

## 概述

系统使用配置驱动的路径管理，所有文件和目录路径都通过配置文件确定。这确保了系统在不同环境下的灵活性和一致性。

## 配置文件位置

在 `config.yaml.example` 中的第 **90-100行** 位置：

```yaml
# ============================================================================
# 路径配置 - 系统文件和目录路径（可选，使用智能默认值）
# ============================================================================

# 数据目录配置 - 所有数据文件的根目录
data_directory: "${DATA_DIRECTORY:-./data}"

# 输出目录配置 - 生成的文档输出位置
output_directory: "${OUTPUT_DIRECTORY:-./data/output}"

# ============================================================================
# 缓存配置 - 智能缺省（可选，系统会自动创建文件缓存）
# ============================================================================

cache:
  # 文件系统缓存基础路径
  filesystem_base_path: "${CACHE_FILESYSTEM_PATH:-./data/cache}"
```

## 配置项详解

### 1. 数据目录配置

```yaml
data_directory: "${DATA_DIRECTORY:-./data}"
```

- **作用**: 设置所有数据文件的根目录
- **默认值**: `./data`（相对于项目根目录）
- **环境变量**: `DATA_DIRECTORY`
- **子目录结构**:
  - `raw_content/` - 原始网页内容
  - `markdown_content/` - 转换后的Markdown文件
  - `locks/` - 处理锁文件
  - `output/` - 输出文档（如果未单独配置）

### 2. 输出目录配置

```yaml
output_directory: "${OUTPUT_DIRECTORY:-./data/output}"
```

- **作用**: 设置生成文档的输出位置
- **默认值**: `./data/output`
- **环境变量**: `OUTPUT_DIRECTORY`
- **用途**: 存储最终生成的技术文档

### 3. 缓存目录配置

```yaml
cache:
  filesystem_base_path: "${CACHE_FILESYSTEM_PATH:-./data/cache}"
```

- **作用**: 设置缓存文件的基础路径
- **默认值**: `./data/cache`
- **环境变量**: `CACHE_FILESYSTEM_PATH`
- **子目录结构**:
  - `url_cache/` - URL处理状态缓存
  - `url_content/` - URL内容缓存（如果启用）

## 配置方式

### 方式1: 配置文件

1. 复制 `config.yaml.example` 为 `config.yaml`
2. 修改路径配置：

```yaml
# 自定义路径示例
data_directory: "/opt/app/data"
output_directory: "/opt/app/output"

cache:
  filesystem_base_path: "/opt/app/cache"
```

### 方式2: 环境变量

```bash
# 设置环境变量
export DATA_DIRECTORY="/opt/app/data"
export OUTPUT_DIRECTORY="/opt/app/output"
export CACHE_FILESYSTEM_PATH="/opt/app/cache"

# 运行程序
python main.py
```

### 方式3: 混合配置

```yaml
# config.yaml - 使用环境变量和默认值
data_directory: "${DATA_DIRECTORY:-/app/data}"
output_directory: "${OUTPUT_DIRECTORY:-/app/output}"

cache:
  filesystem_base_path: "${CACHE_FILESYSTEM_PATH:-/app/cache}"
```

## 路径解析规则

### 1. 绝对路径

如果配置的路径是绝对路径，系统直接使用：

```yaml
data_directory: "/absolute/path/to/data"  # 直接使用
```

### 2. 相对路径

如果配置的路径是相对路径，系统基于项目根目录解析：

```yaml
data_directory: "./data"  # 解析为 /project/root/data
data_directory: "custom"  # 解析为 /project/root/custom
```

### 3. 环境变量

支持环境变量替换和默认值：

```yaml
data_directory: "${DATA_DIR:-./data}"  # 使用环境变量或默认值
```

## 实际使用示例

### 开发环境

```yaml
# config.yaml
data_directory: "./data"
output_directory: "./data/output"

cache:
  filesystem_base_path: "./data/cache"
```

### 生产环境

```yaml
# config.yaml
data_directory: "${DATA_DIRECTORY:-/opt/app/data}"
output_directory: "${OUTPUT_DIRECTORY:-/opt/app/output}"

cache:
  filesystem_base_path: "${CACHE_FILESYSTEM_PATH:-/opt/app/cache}"
```

```bash
# 环境变量
export DATA_DIRECTORY="/var/lib/docwriter/data"
export OUTPUT_DIRECTORY="/var/lib/docwriter/output"
export CACHE_FILESYSTEM_PATH="/var/cache/docwriter"
```

### Docker环境

```yaml
# config.yaml
data_directory: "/app/data"
output_directory: "/app/output"

cache:
  filesystem_base_path: "/app/cache"
```

```dockerfile
# Dockerfile
VOLUME ["/app/data", "/app/output", "/app/cache"]
```

## 目录结构示例

配置生效后的目录结构：

```
项目根目录/
├── data/                    # data_directory
│   ├── raw_content/         # 原始内容
│   ├── markdown_content/    # Markdown文件
│   ├── locks/              # 处理锁
│   └── output/             # 输出文档（默认）
├── cache/                  # cache.filesystem_base_path
│   └── url_cache/          # URL缓存
│       ├── url_status.json # URL状态缓存
│       └── cache_metadata.json
└── config.yaml            # 配置文件
```

## 注意事项

### 1. 权限要求

确保应用程序对配置的目录有读写权限：

```bash
# 设置目录权限
chmod 755 /opt/app/data
chmod 755 /opt/app/cache
chmod 755 /opt/app/output
```

### 2. 目录自动创建

系统会自动创建不存在的目录，但需要对父目录有写权限。

### 3. 路径验证

系统启动时会验证路径配置的有效性，如果路径无效会报错。

### 4. 配置优先级

配置优先级（从高到低）：
1. 环境变量
2. config.yaml 中的配置
3. 系统默认值

## 故障排除

### 问题1: 路径不存在错误

```
Error: [Errno 2] No such file or directory
```

**解决方案**:
1. 检查配置的路径是否正确
2. 确保对父目录有写权限
3. 手动创建目录或检查权限

### 问题2: 权限拒绝错误

```
Error: [Errno 13] Permission denied
```

**解决方案**:
1. 检查目录权限：`ls -la /path/to/directory`
2. 修改权限：`chmod 755 /path/to/directory`
3. 检查用户是否有访问权限

### 问题3: 配置不生效

**解决方案**:
1. 确认 `config.yaml` 文件存在且格式正确
2. 检查环境变量是否正确设置
3. 重启应用程序使配置生效

## 最佳实践

1. **使用环境变量**: 在生产环境中使用环境变量配置路径
2. **绝对路径**: 生产环境建议使用绝对路径
3. **权限最小化**: 只给应用程序必要的目录权限
4. **备份配置**: 保留配置文件的备份
5. **文档记录**: 记录每个环境的路径配置
