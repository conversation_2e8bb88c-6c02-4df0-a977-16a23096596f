
# 技术调研报告编写通用流程


## 总体逻辑

1. “文档编写流程”包括“需求分析 -> 深入调研 -> 文档编写 -> 文档审核 -> 文档发布”五个阶段，其中：
  1.1 “需求分析”阶段
    - 主要目的和输出物：搞清楚用户意图、形成“调研报告提纲”和“调研行动规划”，其中：
      - “调研报告提纲”主要描述交付研究报告的章节结构、要有整体逻辑，要实现用户的意图；章节结构要拆解到 三级标题
      - “调研行动规划”主要描述要从哪些源获取信息、数据来支撑“调研报告提纲”中的内容生成；对于源的描述要具体，要有举例，而不是简单的分类
    - 主要工作方法：通过七个步骤完成需求分析：首先获得并结构化用户输入，然后分析用户意图并拆解出搜索关键词，接着进行初步调研以界定调研范围，收集关键信息源的原始内容，评估信息是否覆盖需求清单并基本支撑用户意图，最后基于评估结果制定调研报告提纲和调研行动规划。
    - 流程：
      1. "获得用户输入"（沟通技能）
         - 输入：用户原始需求描述
         - 输出：结构化用户需求记录
         - 注意事项：输入输出要以文档形式保存下来
      2. "用户意图分析和需求拆解"（分析技能）
         - 输入：结构化用户需求记录
         - 输出：用户意图、具体需求清单、搜索关键词列表
         - 注意事项：
           - 输入输出要以文档形式保存下来
           - 需求清单是层次化，有逻辑的，需要支撑用户意图的实现。所有需求实现了，用户意图也就实现了
           - 搜索关键词要包括英语、汉语；如果搜索对象不是汉语或英语母语国家的，则还需要考虑当地的语言的搜索关键词
      3. "初步调研"（信息检索技能）
         - 输入：搜索关键词列表
         - 输出：相关信息摘要、信息源清单、调研范围界定
         - 注意事项：
           - 输入输出要以文档形式保存下来
           - 要对信息源进行分类，分类要具体，不要笼统
           - 信息源清单使用学术引用 APA 第七版格式
           - 摘要是对信息源的简要概括，要有逻辑，要有重点，要有观点
      4. "收集原始信息"（信息检索技能）
         - 输入：信息源清单
         - 输出：原始信息文件集合（每个信息源一个markdown文件）
         - 注意事项：
           - 逐个访问信息源清单中的每个URL，获取完整网页内容
           - 将网页内容转换为markdown格式并单独保存
           - 每个文件包含：信息源URL、抓取时间、原始内容
      5. "评估信息覆盖度"（分析技能）
         - 输入：用户意图、具体需求清单、相关信息摘要、调研范围界定、原始信息文件集合
         - 输出：需求覆盖度评估、信息缺口识别
         - 注意事项：
           - 输入输出要以文档形式保存下来
           - 逐项检查具体需求清单是否都有对应的信息支撑
           - 判断收集的信息是否基本能支撑用户意图的实现
           - 如有明显缺口，返回步骤3补充关键信息
           - 评估通过后，进入提纲制定阶段
      6. "制定调研报告提纲"（逻辑设计技能）
         - 输入：用户意图、具体需求清单、相关信息摘要、调研范围界定
         - 输出：调研报告提纲
         - 注意事项：
           - 输入输出要以文档形式保存下来
           - 调研报告提纲是用户意图的具体化，是用户需求的具体化
           - 调研报告提纲整体要有逻辑
           - 调研报告提纲要拆解到三级标题
      7. "制定调研行动规划"（项目规划技能）
         - 输入：调研报告提纲、信息源清单、原始信息文件集合
         - 输出：具体信息源描述、数据获取方法、调研任务优先级
         - 注意事项：
           - 输入输出要以文档形式保存下来
  1.2 “深入调研”阶段
    - 主要目的和输出物：根据“调研报告提纲”、“调研行动规划”拆解“调研任务”，每个“调研任务”执行完毕需要完成“调研任务报告”、“调研原始材料清单”和对应的“调研原始材料”。其中：
       - ”调研任务报告“主要是调研数据的摘要、观点
      - ”调研原始材料清单“是”调研任务“收集的所有”调研原始材料“的清单
      - ”调研原始材料“是”调研任务“数据、信息、资料的全部原始信息
    - 流程："拆解调研任务" -> 执行各个"调研任务" -> "汇总调研结果" -> "调研结果研判"
  1.3 ”文档编写“阶段
    - 主要目的和输出物：根据“调研报告提纲”、所有”调研任务“的“调研任务报告”、“调研原始材料清单”和对应的“调研原始材料”，遵循”文档规范“，完成”调研报告“编写
    - 流程："结构化编写" -> "内容润色" -> "完成初稿"
  1.4 ”文档审核“阶段
    - 主要主要目的和输出物：对”调研报告“进行审核，形成”审核意见“
    - 流程："审核报告" -> "形成审核意见"
  1.5 ”文档发布“阶段
    - 主要目的和输出物：完成”调研报告“的发布动作
    - 流程："最终完善" -> "执行发布"
2. 在“文档编写流程”中每个阶段进入下一个阶段都要经过“用户反馈流程”
3. “用户反馈流程”包括“获得用户反馈->用户反馈分析->用户反馈确认->用户反馈响应”四个阶段：
  3.1 “获得用户反馈”阶段
    - 主要目的和输出物：获得用户的反馈意见的输入
    - 流程："获得用户反馈"
  3.2 “用户反馈分析”阶段
    - 主要目的和输出物：对用户反馈意见进行分析，并给出明确判断，并给出具体可行动项
      - 判断是否涉及到“需求增删改”：要给出明确的判断，并明确具体有什么需求的增删改，以及对“调研报告提纲”的逻辑、结构的具体调整内容
      - 判断是否涉及到”补充调研“：要给出明确的判断，以及具体有哪些要补充调研的信息源和调研方向
      - 判断是否涉及到“文字修改”：要给出明确的判断，以及具体要修改的章节、段落、句子，以及对应的修改要求
    - 流程：“用户反馈分析”
  3.3 “用户反馈确认”阶段
    - 主要目的和输出物：则是要求用户对“用户反馈分析”进行确认，判断是否能进行到下一阶段的”用户反馈响应”。如果用户反馈可以，则进行下一阶段；如果反馈不可以或者不明确，则再次跳回“获得用户反馈”阶段，在原有基础上通过“用户反馈流程”进行迭代
    - 流程："展示分析结果" -> "获得用户确认"
  3.3 “用户反馈响应”则是根据用户反馈意见进行迭代
    - 主要目的和输出物：
      - 用户反馈意见的处理优先级：需求增删改 > 补充调研 > 文字修改
      - 用户反馈意见涉及 ”需求增删改“ 则进入 “文档编写流程” 的 ”需求分析“ 阶段，在原有基础上，结合用户反馈意见，完成需求分析
      - 用户反馈意见涉及 ”补充调研“ 则进入 “文档编写流程” 的 ”深入调研“ 阶段，在原有基础上，结合用户反馈意见，完成调研
      - 用户反馈意见涉及 “文字修改” 则进入 “文档编写流程” 的 ”文档编写“ 阶段，在原有基础上，结合用户反馈意见，完成文档编写
    - 流程："执行行动计划" -> "返回对应阶段"


## 流程图

```mermaid
flowchart TD
    Start([开始]) --> DocFlow
    DocFlow --> End([结束])

    %% 文档编写流程主图
    subgraph DocFlow ["文档编写流程"]
        direction TB
        A1_Entry[进入需求分析] --> A1_Sub
        A1_Sub --> FB1{用户反馈流程}
        FB1 -->|通过| A2_Sub
        A2_Sub --> FB2{用户反馈流程}
        FB2 -->|通过| A3_Sub
        A3_Sub --> FB3{用户反馈流程}
        FB3 -->|通过| A4_Sub
        A4_Sub --> FB4{用户反馈流程}
        FB4 -->|通过| A5_Sub
        A5_Sub --> A5_Exit[完成文档发布]

        %% 需求分析阶段子图
        subgraph A1_Sub ["需求分析阶段"]
            direction TB
            A1_1["获得用户输入
            结构化用户需求记录"] --> A1_2["用户意图分析和需求拆解
            用户意图、具体需求清单
            搜索关键词列表"]
            A1_2 --> A1_3["初步调研
            相关信息摘要、信息源清单
            调研范围界定"]
            A1_3 --> A1_4["收集原始信息
            原始信息文件集合
            每个信息源一个markdown文件"]
            A1_4 --> A1_5["评估信息覆盖度
            需求覆盖度评估
            信息缺口识别"]
            A1_5 -->|信息缺口| A1_3
            A1_5 -->|评估通过| A1_6["制定调研报告提纲
            章节结构拆解到三级标题
            整体逻辑实现用户意图"]
            A1_6 --> A1_7["制定调研行动规划
            具体信息源描述
            数据获取方法、任务优先级"]
        end

        %% 深入调研阶段子图
        subgraph A2_Sub ["深入调研阶段"]
            direction TB
            A2_1[拆解调研任务] --> A2_2[执行调研任务]
            A2_2 --> A2_3["完成调研任务报告
            调研数据摘要、观点"]
            A2_3 --> A2_4["完成调研原始材料清单
            所有原始材料的清单"]
            A2_4 --> A2_5["收集调研原始材料
            数据、信息、资料的全部原始信息"]
        end

        %% 文档编写阶段子图
        subgraph A3_Sub ["文档编写阶段"]
            direction TB
            A3_1[基于调研报告提纲] --> A3_2[整合调研任务报告]
            A3_2 --> A3_3[参考调研原始材料]
            A3_3 --> A3_4[遵循文档规范]
            A3_4 --> A3_5[完成调研报告编写]
        end

        %% 文档审核阶段子图
        subgraph A4_Sub ["文档审核阶段"]
            direction TB
            A4_1[审核调研报告] --> A4_2[形成审核意见]
        end

        %% 文档发布阶段子图
        subgraph A5_Sub ["文档发布阶段"]
            direction TB
            A5_1[完成调研报告发布]
        end
    end

    %% 用户反馈流程子图
    subgraph FeedbackFlow ["用户反馈流程"]
        direction TB
        FB_Start[获得用户反馈] --> FB_Analysis[用户反馈分析]
        FB_Analysis --> FB_Analysis_1["判断需求增删改
        明确具体增删改内容
        调整报告提纲逻辑结构"]
        FB_Analysis --> FB_Analysis_2["判断补充调研
        明确信息源和调研方向"]
        FB_Analysis --> FB_Analysis_3["判断文字修改
        明确修改章节段落句子
        具体修改要求"]
        FB_Analysis_1 --> FB_Confirm[用户反馈确认]
        FB_Analysis_2 --> FB_Confirm
        FB_Analysis_3 --> FB_Confirm
        FB_Confirm -->|用户确认可以| FB_Response[用户反馈响应]
        FB_Confirm -->|用户反馈不可以或不明确| FB_Start

        %% 反馈响应的优先级处理
        FB_Response --> FB_Priority{处理优先级判断}
        FB_Priority -->|"需求增删改
        最高优先级"| Back_A1[返回需求分析阶段]
        FB_Priority -->|"补充调研
        中等优先级"| Back_A2[返回深入调研阶段]
        FB_Priority -->|"文字修改
        最低优先级"| Back_A3[返回文档编写阶段]
    end

    %% 反馈流程的连接
    FB1 --> FB_Start
    FB2 --> FB_Start
    FB3 --> FB_Start
    FB4 --> FB_Start

    %% 反馈响应的返回连接
    Back_A1 --> A1_Sub
    Back_A2 --> A2_Sub
    Back_A3 --> A3_Sub

    %% 样式定义
    classDef mainFlow fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef stageSubgraph fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef feedbackSubgraph fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef subProcess fill:#e8f5e8,stroke:#388e3c,stroke-width:1px
    classDef feedback fill:#ffebee,stroke:#d32f2f,stroke-width:1px
    classDef decision fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef startEnd fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef entry fill:#f1f8e9,stroke:#689f38,stroke-width:1px

    class DocFlow mainFlow
    class A1_Sub,A2_Sub,A3_Sub,A4_Sub,A5_Sub stageSubgraph
    class FeedbackFlow feedbackSubgraph
    class A1_1,A1_2,A1_3,A1_4,A1_5,A1_6,A1_7,A2_1,A2_2,A2_3,A2_4,A2_5,A3_1,A3_2,A3_3,A3_4,A3_5,A4_1,A4_2,A5_1 subProcess
    class FB_Start,FB_Analysis,FB_Analysis_1,FB_Analysis_2,FB_Analysis_3,FB_Confirm,FB_Response,Back_A1,Back_A2,Back_A3 feedback
    class FB1,FB2,FB3,FB4,FB_Priority decision
    class Start,End startEnd
    class A1_Entry,A5_Exit entry
```
