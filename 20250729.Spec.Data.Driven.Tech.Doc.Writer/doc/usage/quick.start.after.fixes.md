# 修复后快速开始指南

## 概述

经过修复，系统现在支持：
- ✅ 配置驱动的路径管理
- ✅ 灵活的SSL证书验证配置
- ✅ 完整的环境变量解析（支持 `${VAR:-default}` 语法）
- ✅ 代理配置支持

## 快速开始

### 1. 复制配置文件

```bash
cp config.yaml.example config.yaml
```

### 2. 基本配置（可选）

如果需要自定义配置，编辑 `config.yaml`：

```yaml
# 基本配置 - 使用默认值
llm_primary:
  provider: "openai"
  model: "gpt-4o"
  api_key: "${OPENAI_API_KEY:-your_api_key_here}"

# 路径配置 - 使用默认值
data_directory: "${DATA_DIRECTORY:-./data}"
output_directory: "${OUTPUT_DIRECTORY:-./data/output}"

# 网络配置 - 使用默认值
verify_ssl: "${VERIFY_SSL:-true}"
proxy_url: "${PROXY_URL:-http://127.0.0.1:8118/}"

# 缓存配置 - 使用默认值
cache:
  filesystem_base_path: "${CACHE_FILESYSTEM_PATH:-./data/cache}"
```

### 3. 环境变量配置（推荐）

创建 `.env` 文件或设置环境变量：

```bash
# 必需配置
export OPENAI_API_KEY="your_openai_api_key"

# 可选配置
export VERIFY_SSL=true
export PROXY_URL=http://127.0.0.1:8118/
export DATA_DIRECTORY=./data
export CACHE_FILESYSTEM_PATH=./data/cache
```

### 4. 运行系统

```bash
python main.py
```

## 常见配置场景

### 场景1: 开发环境（默认配置）

```yaml
# config.yaml - 最简配置
llm_primary:
  provider: "openai"
  model: "gpt-4o"
  api_key: "${OPENAI_API_KEY}"

# 其他配置使用默认值
```

```bash
# 环境变量
export OPENAI_API_KEY="your_api_key"
```

### 场景2: SSL证书问题环境

如果遇到SSL证书错误，可以临时禁用SSL验证：

```bash
# 环境变量
export VERIFY_SSL=false
export OPENAI_API_KEY="your_api_key"
```

或在配置文件中：

```yaml
verify_ssl: false
```

### 场景3: 代理环境

```bash
# 环境变量
export PROXY_URL=http://your-proxy:8080/
export VERIFY_SSL=true
export OPENAI_API_KEY="your_api_key"
```

### 场景4: 自定义路径

```bash
# 环境变量
export DATA_DIRECTORY=/opt/app/data
export OUTPUT_DIRECTORY=/opt/app/output
export CACHE_FILESYSTEM_PATH=/opt/app/cache
export OPENAI_API_KEY="your_api_key"
```

### 场景5: 生产环境

```yaml
# config.yaml - 生产环境
llm_primary:
  provider: "openai"
  model: "gpt-4o"
  api_key: "${OPENAI_API_KEY}"

project_root: "${PROJECT_ROOT:-/opt/docwriter}"
data_directory: "${DATA_DIRECTORY:-./data}"
output_directory: "${OUTPUT_DIRECTORY:-./output}"

verify_ssl: "${VERIFY_SSL:-true}"
proxy_url: "${PROXY_URL}"

cache:
  filesystem_base_path: "${CACHE_FILESYSTEM_PATH:-./cache}"
```

```bash
# 环境变量
export PROJECT_ROOT=/opt/docwriter
export OPENAI_API_KEY="production_api_key"
export PROXY_URL=http://production-proxy:8080/
export VERIFY_SSL=true
```

## 验证配置

### 1. 运行验证脚本

```bash
python verify_fixes.py
```

期望输出：
```
🔧 验证修复是否有效
==================================================
🧪 测试环境变量解析器...
✅ 环境变量解析器工作正常

🧪 测试配置模型...
✅ 配置模型包含SSL和代理配置

🧪 测试WebScraper SSL配置...
✅ WebScraper SSL配置工作正常

🧪 测试路径解析...
✅ 路径解析正常，生成绝对路径

==================================================
📊 测试结果: 4/4 通过
🎉 所有修复验证通过！
```

### 2. 检查配置解析

```python
from src.backend.config import Config

config = Config()
print(f"SSL验证: {config.verify_ssl}")
print(f"代理URL: {config.proxy_url}")
print(f"数据目录: {config.data_directory}")
print(f"缓存路径: {config.cache.filesystem_base_path}")
```

## 故障排除

### 问题1: SSL证书错误

```
[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed
```

**解决方案**:
```bash
export VERIFY_SSL=false
```

### 问题2: 环境变量未解析

路径中仍包含 `${VAR:-default}` 字符串

**解决方案**:
1. 检查环境变量是否正确设置
2. 确认配置文件语法正确
3. 重启应用程序

### 问题3: 路径不存在错误

```
[Errno 2] No such file or directory
```

**解决方案**:
1. 检查路径配置是否正确
2. 确保对目录有写权限
3. 系统会自动创建目录

### 问题4: 代理连接失败

**解决方案**:
```bash
# 测试代理连接
curl -x http://127.0.0.1:8118/ https://www.google.com

# 如果代理不可用，可以禁用
export PROXY_URL=""
```

## 配置优先级

1. **环境变量**（最高优先级）
2. **配置文件中的值**
3. **默认值**（最低优先级）

## 安全建议

### 1. 生产环境

- ✅ 启用SSL验证：`VERIFY_SSL=true`
- ✅ 使用环境变量存储敏感信息
- ✅ 设置适当的文件权限

### 2. 开发环境

- ⚠️ 可以禁用SSL验证用于测试
- ✅ 使用 `.env` 文件管理环境变量
- ✅ 不要提交敏感信息到版本控制

## 性能优化

### 1. 缓存配置

```bash
# 使用SSD存储缓存
export CACHE_FILESYSTEM_PATH=/fast/ssd/cache
```

### 2. 代理配置

```bash
# 使用本地代理提高速度
export PROXY_URL=http://127.0.0.1:8118/
```

## 监控和日志

系统会自动记录：
- 配置加载状态
- SSL验证状态
- 代理连接状态
- 路径解析结果

查看日志以确认配置是否正确应用。

## 下一步

配置完成后，您可以：

1. **运行系统**: `python main.py`
2. **处理URL**: 系统会自动处理网页内容
3. **查看输出**: 检查输出目录中的生成文档
4. **监控日志**: 观察系统运行状态

## 支持

如果遇到问题：

1. 运行 `python verify_fixes.py` 检查基本功能
2. 检查日志文件中的错误信息
3. 确认环境变量和配置文件设置
4. 参考故障排除部分

修复后的系统现在应该能够稳定运行，不再出现SSL证书和路径相关的错误！
