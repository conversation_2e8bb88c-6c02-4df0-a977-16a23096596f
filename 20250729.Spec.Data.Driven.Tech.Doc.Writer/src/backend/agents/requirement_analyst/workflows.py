"""
需求分析工作流函数

基于LangGraph 0.6函数式API的工作流函数。
"""

from typing import Dict, Any, List
import structlog
import asyncio
from datetime import datetime
from pathlib import Path

from src.backend.agents.base import AgentConfig
from src.backend.models.request import UserRequest, RequirementAnalysis, SearchKeywords
from src.backend.agents.search_engines import SearchEngineService
from src.backend.agents.web_content_processor import WebContentProcessor
from src.backend.agents.url_cache_manager import URLCacheManager
from .core import RequirementAnalystAgent

logger = structlog.get_logger(__name__)


# 临时移除@task装饰器，等待LangGraph 0.6函数式API正式发布
async def parse_user_input(user_request: UserRequest) -> Dict[str, Any]:
    """解析用户输入的@task函数"""
    agent_config = AgentConfig(
        agent_id="requirement_analyst_parser",
        agent_type="requirement_analyst",
        llm_config={}
    )

    # 从配置中获取真实的system_config
    from src.backend.config import get_config
    system_config = await get_config()

    agent = RequirementAnalystAgent(agent_config, system_config)
    await agent.initialize()

    result = await agent.execute_task(user_request)
    return result.result_data


# 临时移除@task装饰器，等待LangGraph 0.6函数式API正式发布
async def generate_search_keywords(analysis: RequirementAnalysis) -> SearchKeywords:
    """生成搜索关键词的@task函数"""
    # 基于需求分析生成关键词
    primary_keywords = []
    secondary_keywords = []
    technical_terms = []
    
    # 从主题中提取关键词
    topic_words = analysis.parsed_topic.split()
    primary_keywords.extend(topic_words[:3])
    
    # 从目标中提取关键词
    for objective in analysis.research_objectives:
        words = objective.split()
        technical_terms.extend([w for w in words if len(w) > 3])
    
    # 去重并限制数量
    primary_keywords = list(set(primary_keywords))[:5]
    technical_terms = list(set(technical_terms))[:10]
    
    return SearchKeywords(
        requirement_analysis_id=analysis.user_request_id,
        primary_keywords=primary_keywords,
        secondary_keywords=secondary_keywords,
        technical_terms=technical_terms
    )


# 临时移除@entrypoint装饰器，等待LangGraph 0.6函数式API正式发布
async def requirement_analysis_workflow(user_request: UserRequest) -> Dict[str, Any]:
    """需求分析工作流"""
    logger.info("Starting requirement analysis workflow", user_request_id=user_request.id)

    # 解析用户输入
    parsed_data = await parse_user_input(user_request)

    # 重构为RequirementAnalysis对象
    analysis = RequirementAnalysis(**parsed_data)

    # 生成搜索关键词
    keywords = await generate_search_keywords(analysis)

    # 执行初步调研
    logger.info("Starting preliminary research", keywords_count=len(keywords.primary_keywords))
    preliminary_research_result = await preliminary_research(keywords)

    # 收集原始信息
    logger.info("Starting raw information collection")
    raw_information_result = await collect_raw_information(preliminary_research_result["source_list"])

    logger.info("Requirement analysis workflow completed",
               user_request_id=user_request.id,
               research_sources_found=preliminary_research_result.get("metadata", {}).get("total_sources", 0),
               raw_information_files=len(raw_information_result.get("raw_information_files", [])))

    return {
        "analysis": analysis.model_dump(),
        "keywords": keywords.model_dump(),
        "preliminary_research": preliminary_research_result,
        "raw_information": raw_information_result
    }


# 临时移除@task装饰器，等待LangGraph 0.6函数式API正式发布
async def preliminary_research(keywords: SearchKeywords) -> Dict[str, Any]:
    """
    初步调研工作流函数

    基于搜索关键词执行初步调研，生成信息摘要、信息源清单和调研范围界定。

    Args:
        keywords: 搜索关键词对象

    Returns:
        包含调研结果的字典
    """
    logger.info("Starting preliminary research", keywords_count=len(keywords.primary_keywords))

    try:
        # 获取配置
        from src.backend.config import get_config
        config = await get_config()

        # 初始化搜索引擎服务
        search_service = SearchEngineService(config)

        # 准备搜索查询列表
        search_queries = _prepare_search_queries(keywords, config)

        # 并行执行搜索
        search_results = await _parallel_search(search_service, search_queries)

        # 处理和分析结果
        processed_results = _process_search_results(search_results)

        # 生成信息摘要
        information_summary = _generate_information_summary(processed_results, config)

        # 生成信息源清单
        source_list = _generate_source_list(processed_results, config)

        # 界定调研范围
        research_scope = _define_research_scope(processed_results, keywords)

        # 构建输出结果
        result = {
            "information_summary": information_summary,
            "source_list": source_list,
            "research_scope": research_scope,
            "metadata": {
                "search_queries": search_queries,
                "total_sources": len(processed_results),
                "timestamp": datetime.now().isoformat(),
                "search_engines_used": _get_engines_used(search_results)
            }
        }

        logger.info(
            "Preliminary research completed",
            total_sources=len(processed_results),
            queries_count=len(search_queries)
        )

        return result

    except Exception as e:
        logger.error("Preliminary research failed", error=str(e))
        raise


def _prepare_search_queries(keywords: SearchKeywords, config: Optional[Any] = None) -> List[str]:
    """准备搜索查询列表"""
    # 获取配置参数
    if config and hasattr(config, 'retrieval') and hasattr(config.retrieval, 'research'):
        research_config = config.retrieval.research
        primary_limit = research_config.primary_keywords_limit
        technical_limit = research_config.technical_terms_limit
        english_limit = research_config.english_keywords_limit
        query_limit = research_config.search_query_limit
    else:
        # 使用默认值（向后兼容）
        primary_limit = 3
        technical_limit = 2
        english_limit = 2
        query_limit = 5

    queries = []

    # 主要关键词查询
    for keyword in keywords.primary_keywords[:primary_limit]:
        queries.append(keyword)

    # 组合查询
    if len(keywords.primary_keywords) >= 2:
        combined_query = " ".join(keywords.primary_keywords[:2])
        queries.append(combined_query)

    # 技术术语查询
    for term in keywords.technical_terms[:technical_limit]:
        queries.append(term)

    # 英文关键词查询
    for keyword in keywords.english_keywords[:english_limit]:
        queries.append(keyword)

    # 去重并限制总数
    unique_queries = list(dict.fromkeys(queries))  # 保持顺序的去重
    return unique_queries[:query_limit]


async def _parallel_search(search_service: SearchEngineService, queries: List[str]) -> List[Dict[str, Any]]:
    """并行执行搜索"""
    logger.info("Starting parallel search", queries_count=len(queries))

    # 从配置中获取 max_results，如果没有配置则使用默认值 8
    max_results_per_query = 8
    try:
        config = search_service.config
        if hasattr(config, 'retrieval') and hasattr(config.retrieval, 'search_engines'):
            max_results_per_query = getattr(config.retrieval.search_engines, 'max_results', 8)
    except Exception:
        # 如果获取配置失败，使用默认值
        pass

    logger.info("Using max_results per query", max_results=max_results_per_query)

    # 创建并行搜索任务
    search_tasks = []
    for query in queries:
        task = search_service.search(query, max_results=max_results_per_query)
        search_tasks.append(task)

    # 并行执行所有搜索
    results = await asyncio.gather(*search_tasks, return_exceptions=True)

    # 处理结果和异常
    valid_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.warning("Search failed for query", query=queries[i], error=str(result))
        else:
            valid_results.append(result)

    logger.info("Parallel search completed", successful_searches=len(valid_results))
    return valid_results


def _process_search_results(search_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """处理搜索结果，合并和去重"""
    all_results = []

    for search_result in search_results:
        if "results" in search_result:
            all_results.extend(search_result["results"])

    # 基于URL去重
    seen_urls = set()
    deduplicated_results = []

    for result in all_results:
        url = result.get("url", "")
        if url and url not in seen_urls:
            seen_urls.add(url)
            deduplicated_results.append(result)

    # 按质量评分排序
    deduplicated_results.sort(key=lambda x: x.get("quality_score", 0), reverse=True)

    logger.info(
        "Search results processed",
        total_results=len(all_results),
        deduplicated_results=len(deduplicated_results)
    )

    return deduplicated_results


def _generate_information_summary(results: List[Dict[str, Any]], config: Optional[Any] = None) -> str:
    """生成信息摘要"""
    if not results:
        return "未找到相关信息。"

    # 获取配置参数
    if config and hasattr(config, 'retrieval') and hasattr(config.retrieval, 'research'):
        summary_limit = config.retrieval.research.information_summary_limit
    else:
        # 使用默认值（向后兼容）
        summary_limit = 10

    # 按来源分类
    sources_by_type = {
        "学术资源": [],
        "行业报告": [],
        "新闻资讯": [],
        "技术文档": [],
        "其他资源": []
    }

    for result in results[:summary_limit]:  # 处理配置数量的高质量结果
        source_type = _classify_source(result)
        sources_by_type[source_type].append(result)

    # 生成摘要
    summary_parts = []

    for source_type, source_results in sources_by_type.items():
        if source_results:
            count = len(source_results)
            summary_parts.append(f"发现 {count} 个{source_type}")

    if summary_parts:
        summary = f"初步调研发现：{', '.join(summary_parts)}。"
    else:
        summary = "初步调研完成，但未发现高质量的相关资源。"

    # 添加主要发现
    if results:
        top_result = results[0]
        summary += f" 主要发现：{top_result.get('content_summary', '暂无摘要')}。"

    return summary


def _generate_source_list(results: List[Dict[str, Any]], config: Optional[Any] = None) -> List[Dict[str, Any]]:
    """生成信息源清单"""
    # 获取配置参数
    if config and hasattr(config, 'retrieval') and hasattr(config.retrieval, 'research'):
        max_sources = config.retrieval.research.max_source_list_size
    else:
        # 使用默认值（向后兼容）
        max_sources = 15

    source_list = []

    for result in results[:max_sources]:  # 使用配置的源数量限制
        source_info = {
            "title": result.get("title", "未知标题"),
            "url": result.get("url", ""),
            "source_type": _classify_source(result),
            "quality_score": result.get("quality_score", 0),
            "relevance_score": result.get("relevance_score", 0),
            "apa_citation": result.get("apa_citation", ""),
            "content_summary": result.get("content_summary", ""),
            "publication_date": result.get("publication_date"),
            "authors": result.get("authors", [])
        }
        source_list.append(source_info)

    logger.info("Source list generated",
                total_results=len(results),
                selected_sources=len(source_list),
                max_sources_config=max_sources)

    return source_list


def _define_research_scope(results: List[Dict[str, Any]], keywords: SearchKeywords) -> Dict[str, Any]:
    """界定调研范围"""
    scope = {
        "primary_topics": keywords.primary_keywords,
        "technical_areas": keywords.technical_terms,
        "research_depth": "初步调研",
        "information_coverage": {},
        "recommended_next_steps": []
    }

    # 分析信息覆盖度
    source_types = {}
    for result in results:
        source_type = _classify_source(result)
        source_types[source_type] = source_types.get(source_type, 0) + 1

    scope["information_coverage"] = source_types

    # 推荐下一步行动
    if source_types.get("学术资源", 0) < 3:
        scope["recommended_next_steps"].append("需要补充更多学术资源")

    if source_types.get("行业报告", 0) < 2:
        scope["recommended_next_steps"].append("需要获取相关行业报告")

    if len(results) < 10:
        scope["recommended_next_steps"].append("需要扩大搜索范围或调整关键词")

    return scope


def _classify_source(result: Dict[str, Any]) -> str:
    """分类信息源"""
    url = result.get("url", "").lower()
    title = result.get("title", "").lower()

    # 学术资源
    if any(domain in url for domain in ["arxiv.org", "scholar.google", "pubmed", "ieee", "acm.org"]):
        return "学术资源"

    # 行业报告
    if any(keyword in url or keyword in title for keyword in ["report", "analysis", "market", "industry"]):
        return "行业报告"

    # 新闻资讯
    if any(domain in url for domain in ["news", "techcrunch", "wired", "reuters", "bloomberg"]):
        return "新闻资讯"

    # 技术文档
    if any(domain in url for domain in ["github.com", "docs.", "documentation", "api.", "developer"]):
        return "技术文档"

    return "其他资源"


def _get_engines_used(search_results: List[Dict[str, Any]]) -> List[str]:
    """获取使用的搜索引擎列表"""
    engines = set()

    for result in search_results:
        if "metadata" in result:
            primary_engine = result["metadata"].get("primary_engine")
            if primary_engine:
                engines.add(primary_engine)

    return list(engines)


# 临时移除@task装饰器，等待LangGraph 0.6函数式API正式发布
async def collect_raw_information(source_list: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    收集原始信息工作流函数 (重构版本)

    基于信息源清单收集原始信息，生成原始信息文件集合。
    使用新的缓存管理器和文件存储结构。

    Args:
        source_list: 信息源清单

    Returns:
        包含原始信息文件集合的字典
    """
    logger.info("Starting raw information collection", sources_count=len(source_list))

    try:
        # 获取配置
        from src.backend.config import get_config
        config = await get_config()

        # 初始化组件 - 使用重构后的处理器
        content_processor = WebContentProcessor(config)
        await content_processor.initialize()

        # 提取URL列表
        urls = []
        for source in source_list:
            url = source.get("url", "")
            if url and url.startswith(('http://', 'https://')):
                # 检查是否已经处理过（新的缓存管理器已集成到处理器中）
                if not await content_processor.cache_manager.is_url_processed(url):
                    urls.append(url)
                else:
                    logger.debug("URL already processed, skipping", url=url)

        logger.info("URLs to process", total_urls=len(urls), cached_urls=len(source_list) - len(urls))

        if not urls:
            logger.info("No new URLs to process, all sources are cached")
            return {
                "raw_information_files": [],
                "metadata": {
                    "total_sources": len(source_list),
                    "processed_sources": 0,
                    "cached_sources": len(source_list),
                    "successful_extractions": 0,
                    "failed_extractions": 0,
                    "timestamp": datetime.now().isoformat()
                }
            }

        # 并发处理URL - 新的处理器已经集成了缓存、锁和文件保存
        max_concurrent = 5  # 限制并发数，避免对目标网站造成过大压力
        processing_results = await content_processor.process_urls(urls, max_concurrent)

        # 分析处理结果
        raw_information_files = []
        successful_extractions = 0
        failed_extractions = 0

        for result in processing_results:
            url = result["url"]
            success = result.get("success", False)

            # 新的处理器已经自动处理了缓存更新和文件保存
            if success and not result.get("cached", False):
                # 检查是否有质量评估结果
                quality_result = result.get("quality_result", {})
                passes_quality = quality_result.get("passes_quality", False)

                if passes_quality:
                    # 构建文件信息
                    raw_information_files.append({
                        "url": url,
                        "raw_content_path": result.get("raw_content_path", ""),
                        "markdown_path": result.get("markdown_path", ""),
                        "title": quality_result.get("title", ""),
                        "quality_score": quality_result.get("quality_score", 0),
                        "word_count": quality_result.get("word_count", 0),
                        "content_summary": quality_result.get("content_summary", ""),
                        "processing_time": result.get("processing_time", 0)
                    })
                    successful_extractions += 1
                else:
                    failed_extractions += 1
                    logger.info(
                        "Content filtered due to quality",
                        url=url,
                        quality_score=quality_result.get("quality_score", 0)
                    )
            elif result.get("cached", False):
                # 缓存命中，从缓存获取信息
                logger.debug("Using cached content", url=url)
                # 可以选择从缓存中获取文件路径信息
                successful_extractions += 1
            else:
                failed_extractions += 1
                logger.info(
                    "Content processing failed",
                    url=url,
                    error=result.get("error", "Unknown error"),
                    stage=result.get("stage", "unknown")
                )

        # 获取处理统计
        processor_stats = content_processor.stats
        cache_stats = content_processor.cache_manager.get_cache_stats()

        # 构建结果
        result = {
            "raw_information_files": raw_information_files,
            "metadata": {
                "total_sources": len(source_list),
                "processed_sources": len(urls),
                "cached_sources": len(source_list) - len(urls),
                "successful_extractions": successful_extractions,
                "failed_extractions": failed_extractions,
                "timestamp": datetime.now().isoformat(),
                "processor_stats": processor_stats,
                "cache_stats": cache_stats,
                "storage_structure": {
                    "raw_content_dir": "data/raw_content",
                    "markdown_content_dir": "data/markdown_content",
                    "cache_dir": "data/cache/url_cache"
                }
            }
        }

        # 清理资源
        await content_processor.cleanup()

        logger.info(
            "Raw information collection completed",
            total_sources=len(source_list),
            successful_extractions=successful_extractions,
            failed_extractions=failed_extractions,
            cache_hits=processor_stats.get("cache_hits", 0),
            concurrent_skips=processor_stats.get("concurrent_skips", 0),
            raw_content_dir="data/raw_content",
            markdown_content_dir="data/markdown_content"
        )

        return result

    except Exception as e:
        logger.error("Raw information collection failed", error=str(e))
        raise
